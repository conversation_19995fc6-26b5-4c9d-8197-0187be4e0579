{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\nav-profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Menu, Dropdown, Modal } from 'antd';\nimport { EditOutlined, LogoutOutlined } from '@ant-design/icons';\nimport getAvatar from '../helpers/getAvatar';\nimport { useTranslation } from 'react-i18next';\nimport UserModal from './user-modal';\nimport { batch, shallowEqual, useSelector, useDispatch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { clearMenu } from '../redux/slices/menu';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/auth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function NavProfile({\n  user\n}) {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [userModal, setUserModal] = useState(false);\n  const showModal = () => setIsModalVisible(true);\n  const handleCancel = () => setIsModalVisible(false);\n  const {\n    firebaseToken\n  } = useSelector(state => state.auth, shallowEqual);\n  const handleOk = () => {\n    authService.logout({\n      token: firebaseToken\n    }).then(res => {\n      batch(() => {\n        dispatch(clearUser());\n        dispatch(clearMenu());\n        dispatch(setCurrentChat(null));\n      });\n      setIsModalVisible(false);\n      localStorage.removeItem('token');\n      navigate('/login');\n    });\n  };\n  const profileMenuItems = [{\n    key: 'edit.profile',\n    icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this),\n    label: t('edit.profile'),\n    onClick: () => setUserModal(true)\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this),\n    label: t('logout'),\n    onClick: () => showModal()\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n      placement: \"bottom\",\n      overlay: profileMenu,\n      trigger: ['click'],\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-brand cursor-pointer\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"sidebar-logo\",\n          src: getAvatar(user.img),\n          alt: user.fullName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-brand-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"user-name fw-bold\",\n            children: user.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"user-status\",\n            children: user.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      visible: isModalVisible,\n      onOk: handleOk,\n      onCancel: handleCancel,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(LogoutOutlined, {\n        style: {\n          fontSize: '25px',\n          color: '#08c'\n        },\n        theme: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: t('leave.site')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), userModal && /*#__PURE__*/_jsxDEV(UserModal, {\n      visible: userModal,\n      handleCancel: () => setUserModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(NavProfile, \"HBWqYvMVFDlLwakDAl8yqEinOQI=\", false, function () {\n  return [useTranslation, useDispatch, useNavigate, useSelector];\n});\n_c = NavProfile;\nvar _c;\n$RefreshReg$(_c, \"NavProfile\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Dropdown", "Modal", "EditOutlined", "LogoutOutlined", "get<PERSON><PERSON><PERSON>", "useTranslation", "UserModal", "batch", "shallowEqual", "useSelector", "useDispatch", "clearUser", "clearMenu", "setCurrentChat", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NavProfile", "user", "_s", "t", "dispatch", "navigate", "isModalVisible", "setIsModalVisible", "userModal", "setUserModal", "showModal", "handleCancel", "firebaseToken", "state", "auth", "handleOk", "logout", "token", "then", "res", "localStorage", "removeItem", "profileMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "onClick", "children", "placement", "overlay", "profileMenu", "trigger", "className", "src", "img", "alt", "fullName", "role", "visible", "onOk", "onCancel", "centered", "style", "fontSize", "color", "theme", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/nav-profile.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Menu, Dropdown, Modal } from 'antd';\nimport { EditOutlined, LogoutOutlined } from '@ant-design/icons';\nimport getAvatar from '../helpers/getAvatar';\nimport { useTranslation } from 'react-i18next';\nimport UserModal from './user-modal';\nimport { batch, shallowEqual, useSelector, useDispatch } from 'react-redux';\nimport { clearUser } from '../redux/slices/auth';\nimport { clearMenu } from '../redux/slices/menu';\nimport { setCurrentChat } from '../redux/slices/chat';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/auth';\n\nexport default function NavProfile({ user }) {\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [userModal, setUserModal] = useState(false);\n\n  const showModal = () => setIsModalVisible(true);\n  const handleCancel = () => setIsModalVisible(false);\n\n  const { firebaseToken } = useSelector((state) => state.auth, shallowEqual);\n\n  const handleOk = () => {\n    authService.logout({ token: firebaseToken }).then((res) => {\n      batch(() => {\n        dispatch(clearUser());\n        dispatch(clearMenu());\n        dispatch(setCurrentChat(null));\n      });\n      setIsModalVisible(false);\n      localStorage.removeItem('token');\n      navigate('/login');\n    });\n  };\n\n  const profileMenuItems = [\n    {\n      key: 'edit.profile',\n      icon: <EditOutlined />,\n      label: t('edit.profile'),\n      onClick: () => setUserModal(true),\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: t('logout'),\n      onClick: () => showModal(),\n    },\n  ];\n  return (\n    <>\n      <Dropdown placement='bottom' overlay={profileMenu} trigger={['click']}>\n        <div className='sidebar-brand cursor-pointer'>\n          <img\n            className='sidebar-logo'\n            src={getAvatar(user.img)}\n            alt={user.fullName}\n          />\n          <div className='sidebar-brand-text'>\n            <h5 className='user-name fw-bold'>{user.fullName}</h5>\n            <h6 className='user-status'>{user.role}</h6>\n          </div>\n        </div>\n      </Dropdown>\n      <Modal\n        visible={isModalVisible}\n        onOk={handleOk}\n        onCancel={handleCancel}\n        centered\n      >\n        <LogoutOutlined\n          style={{ fontSize: '25px', color: '#08c' }}\n          theme='primary'\n        />\n        <span className='ml-2'>{t('leave.site')}</span>\n      </Modal>\n      {userModal && (\n        <UserModal\n          visible={userModal}\n          handleCancel={() => setUserModal(false)}\n        />\n      )}\n    </>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAChE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,KAAK,EAAEC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAC3E,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,eAAe,SAASC,UAAUA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAE,CAAC,GAAGlB,cAAc,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMgC,SAAS,GAAGA,CAAA,KAAMH,iBAAiB,CAAC,IAAI,CAAC;EAC/C,MAAMI,YAAY,GAAGA,CAAA,KAAMJ,iBAAiB,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAEK;EAAc,CAAC,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAE1B,YAAY,CAAC;EAE1E,MAAM2B,QAAQ,GAAGA,CAAA,KAAM;IACrBpB,WAAW,CAACqB,MAAM,CAAC;MAAEC,KAAK,EAAEL;IAAc,CAAC,CAAC,CAACM,IAAI,CAAEC,GAAG,IAAK;MACzDhC,KAAK,CAAC,MAAM;QACViB,QAAQ,CAACb,SAAS,CAAC,CAAC,CAAC;QACrBa,QAAQ,CAACZ,SAAS,CAAC,CAAC,CAAC;QACrBY,QAAQ,CAACX,cAAc,CAAC,IAAI,CAAC,CAAC;MAChC,CAAC,CAAC;MACFc,iBAAiB,CAAC,KAAK,CAAC;MACxBa,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChChB,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,gBAAgB,GAAG,CACvB;IACEC,GAAG,EAAE,cAAc;IACnBC,IAAI,eAAE3B,OAAA,CAACf,YAAY;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE1B,CAAC,CAAC,cAAc,CAAC;IACxB2B,OAAO,EAAEA,CAAA,KAAMrB,YAAY,CAAC,IAAI;EAClC,CAAC,EACD;IACEc,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAE3B,OAAA,CAACd,cAAc;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE1B,CAAC,CAAC,QAAQ,CAAC;IAClB2B,OAAO,EAAEA,CAAA,KAAMpB,SAAS,CAAC;EAC3B,CAAC,CACF;EACD,oBACEb,OAAA,CAAAE,SAAA;IAAAgC,QAAA,gBACElC,OAAA,CAACjB,QAAQ;MAACoD,SAAS,EAAC,QAAQ;MAACC,OAAO,EAAEC,WAAY;MAACC,OAAO,EAAE,CAAC,OAAO,CAAE;MAAAJ,QAAA,eACpElC,OAAA;QAAKuC,SAAS,EAAC,8BAA8B;QAAAL,QAAA,gBAC3ClC,OAAA;UACEuC,SAAS,EAAC,cAAc;UACxBC,GAAG,EAAErD,SAAS,CAACiB,IAAI,CAACqC,GAAG,CAAE;UACzBC,GAAG,EAAEtC,IAAI,CAACuC;QAAS;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACF/B,OAAA;UAAKuC,SAAS,EAAC,oBAAoB;UAAAL,QAAA,gBACjClC,OAAA;YAAIuC,SAAS,EAAC,mBAAmB;YAAAL,QAAA,EAAE9B,IAAI,CAACuC;UAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtD/B,OAAA;YAAIuC,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAE9B,IAAI,CAACwC;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACX/B,OAAA,CAAChB,KAAK;MACJ6D,OAAO,EAAEpC,cAAe;MACxBqC,IAAI,EAAE5B,QAAS;MACf6B,QAAQ,EAAEjC,YAAa;MACvBkC,QAAQ;MAAAd,QAAA,gBAERlC,OAAA,CAACd,cAAc;QACb+D,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAC3CC,KAAK,EAAC;MAAS;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACF/B,OAAA;QAAMuC,SAAS,EAAC,MAAM;QAAAL,QAAA,EAAE5B,CAAC,CAAC,YAAY;MAAC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EACPpB,SAAS,iBACRX,OAAA,CAACX,SAAS;MACRwD,OAAO,EAAElC,SAAU;MACnBG,YAAY,EAAEA,CAAA,KAAMF,YAAY,CAAC,KAAK;IAAE;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CACF;EAAA,eACD,CAAC;AAEP;AAAC1B,EAAA,CA1EuBF,UAAU;EAAA,QAClBf,cAAc,EACXK,WAAW,EACXI,WAAW,EAOFL,WAAW;AAAA;AAAA6D,EAAA,GAVflD,UAAU;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}