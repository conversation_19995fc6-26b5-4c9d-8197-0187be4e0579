{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\filter-column.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Button, Dropdown, Space, Switch, Typography } from 'antd';\nimport { TableOutlined } from '@ant-design/icons';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nconst FilterColumns = ({\n  columns = [],\n  setColumns,\n  style,\n  size = '',\n  iconOnly\n}) => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [open, setOpen] = useState(false);\n  const menu = /*#__PURE__*/_jsxDEV(Menu, {\n    children: columns === null || columns === void 0 ? void 0 : columns.map((item, key) => /*#__PURE__*/_jsxDEV(Menu.Item, {\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        className: \"d-flex justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Switch, {\n          defaultChecked: true,\n          onClick: () => onChange(item),\n          disabled:\n          // item.title === 'Title' ||\n          // item.title === 'Name' ||\n          // item.title === 'Client'\n          key === 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this)\n    }, item + key, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n  const handleVisibleChange = flag => {\n    setOpen(flag);\n  };\n  function onChange(checked) {\n    const newArray = columns === null || columns === void 0 ? void 0 : columns.map(item => {\n      if (item.dataIndex === checked.dataIndex) {\n        item.is_show = !(item !== null && item !== void 0 && item.is_show);\n      }\n      return item;\n    });\n    setColumns(newArray);\n  }\n  return /*#__PURE__*/_jsxDEV(Dropdown, {\n    overlay: menu,\n    trigger: ['click'],\n    onVisibleChange: handleVisibleChange,\n    visible: open,\n    children: /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: t('change.columns'),\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        style: {\n          ...style\n        },\n        size: size,\n        icon: /*#__PURE__*/_jsxDEV(TableOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 56\n        }, this),\n        children: iconOnly ? null : t('Columns')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(FilterColumns, \"mWpx7w3TVkgqFWGcbCsvWfAtUa8=\", false, function () {\n  return [useTranslation];\n});\n_c = FilterColumns;\nexport default FilterColumns;\nvar _c;\n$RefreshReg$(_c, \"FilterColumns\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "<PERSON><PERSON>", "Dropdown", "Space", "Switch", "Typography", "TableOutlined", "useTranslation", "jsxDEV", "_jsxDEV", "Text", "FilterColumns", "columns", "setColumns", "style", "size", "iconOnly", "_s", "t", "open", "<PERSON><PERSON><PERSON>", "menu", "<PERSON><PERSON>", "children", "map", "item", "key", "<PERSON><PERSON>", "className", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultChecked", "onClick", "onChange", "disabled", "handleVisibleChange", "flag", "checked", "newArray", "dataIndex", "is_show", "overlay", "trigger", "onVisibleChange", "visible", "<PERSON><PERSON><PERSON>", "icon", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/filter-column.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport {\n  Button,\n  Dropdown,\n  Space,\n  Switch,\n  Typography,\n} from 'antd';\nimport { TableOutlined } from '@ant-design/icons';\nimport { useTranslation } from 'react-i18next';\nconst { Text } = Typography;\n\nconst FilterColumns = ({\n  columns = [],\n  setColumns,\n  style,\n  size = '',\n  iconOnly,\n}) => {\n  const { t } = useTranslation();\n  const [open, setOpen] = useState(false);\n  const menu = (\n    <Menu>\n      {columns?.map((item, key) => (\n        <Menu.Item key={item + key}>\n          <Space className='d-flex justify-content-between'>\n            <Text>{item.title}</Text>\n            <Switch\n              defaultChecked\n              onClick={() => onChange(item)}\n              disabled={\n                // item.title === 'Title' ||\n                // item.title === 'Name' ||\n                // item.title === 'Client'\n                key === 1\n              }\n            />\n          </Space>\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  const handleVisibleChange = (flag) => {\n    setOpen(flag);\n  };\n  function onChange(checked) {\n    const newArray = columns?.map((item) => {\n      if (item.dataIndex === checked.dataIndex) {\n        item.is_show = !item?.is_show;\n      }\n      return item;\n    });\n    setColumns(newArray);\n  }\n\n  return (\n    <Dropdown\n      overlay={menu}\n      trigger={['click']}\n      onVisibleChange={handleVisibleChange}\n      visible={open}\n    >\n      <Tooltip title={t('change.columns')}>\n        <Button style={{ ...style }} size={size} icon={<TableOutlined />}>\n          {iconOnly ? null : t('Columns')}\n        </Button>\n      </Tooltip>\n    </Dropdown>\n  );\n};\n\nexport default FilterColumns;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,UAAU,QACL,MAAM;AACb,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAM;EAAEC;AAAK,CAAC,GAAGL,UAAU;AAE3B,MAAMM,aAAa,GAAGA,CAAC;EACrBC,OAAO,GAAG,EAAE;EACZC,UAAU;EACVC,KAAK;EACLC,IAAI,GAAG,EAAE;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAMsB,IAAI,gBACRZ,OAAA,CAACa,IAAI;IAAAC,QAAA,EACFX,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEY,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACtBjB,OAAA,CAACa,IAAI,CAACK,IAAI;MAAAJ,QAAA,eACRd,OAAA,CAACN,KAAK;QAACyB,SAAS,EAAC,gCAAgC;QAAAL,QAAA,gBAC/Cd,OAAA,CAACC,IAAI;UAAAa,QAAA,EAAEE,IAAI,CAACI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzBxB,OAAA,CAACL,MAAM;UACL8B,cAAc;UACdC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACX,IAAI,CAAE;UAC9BY,QAAQ;UACN;UACA;UACA;UACAX,GAAG,KAAK;QACT;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC,GAbMR,IAAI,GAAGC,GAAG;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcf,CACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;EAED,MAAMK,mBAAmB,GAAIC,IAAI,IAAK;IACpCnB,OAAO,CAACmB,IAAI,CAAC;EACf,CAAC;EACD,SAASH,QAAQA,CAACI,OAAO,EAAE;IACzB,MAAMC,QAAQ,GAAG7B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEY,GAAG,CAAEC,IAAI,IAAK;MACtC,IAAIA,IAAI,CAACiB,SAAS,KAAKF,OAAO,CAACE,SAAS,EAAE;QACxCjB,IAAI,CAACkB,OAAO,GAAG,EAAClB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,OAAO;MAC/B;MACA,OAAOlB,IAAI;IACb,CAAC,CAAC;IACFZ,UAAU,CAAC4B,QAAQ,CAAC;EACtB;EAEA,oBACEhC,OAAA,CAACP,QAAQ;IACP0C,OAAO,EAAEvB,IAAK;IACdwB,OAAO,EAAE,CAAC,OAAO,CAAE;IACnBC,eAAe,EAAER,mBAAoB;IACrCS,OAAO,EAAE5B,IAAK;IAAAI,QAAA,eAEdd,OAAA,CAACuC,OAAO;MAACnB,KAAK,EAAEX,CAAC,CAAC,gBAAgB,CAAE;MAAAK,QAAA,eAClCd,OAAA,CAACR,MAAM;QAACa,KAAK,EAAE;UAAE,GAAGA;QAAM,CAAE;QAACC,IAAI,EAAEA,IAAK;QAACkC,IAAI,eAAExC,OAAA,CAACH,aAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAV,QAAA,EAC9DP,QAAQ,GAAG,IAAI,GAAGE,CAAC,CAAC,SAAS;MAAC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEf,CAAC;AAAChB,EAAA,CA1DIN,aAAa;EAAA,QAOHJ,cAAc;AAAA;AAAA2C,EAAA,GAPxBvC,aAAa;AA4DnB,eAAeA,aAAa;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}