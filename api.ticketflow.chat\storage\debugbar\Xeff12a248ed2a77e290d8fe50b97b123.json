{"__meta": {"id": "Xeff12a248ed2a77e290d8fe50b97b123", "datetime": "2025-07-22 12:24:21", "utime": 1753197861.116432, "method": "POST", "uri": "/api/v1/dashboard/admin/shops/61f7e2a5-ab4b-400e-aa62-a6d2829c0e51/verify", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:24:20] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753197860.80275, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753197860.574853, "end": 1753197861.116449, "duration": 0.5415961742401123, "duration_str": "542ms", "measures": [{"label": "Booting", "start": 1753197860.574853, "relative_start": 0, "end": 1753197860.785575, "relative_end": 1753197860.785575, "duration": 0.2107219696044922, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753197860.785585, "relative_start": 0.21073198318481445, "end": 1753197861.116451, "relative_end": 1.9073486328125e-06, "duration": 0.33086609840393066, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 41817240, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/admin/shops/{uuid}/verify", "middleware": "api, block.ip, sanctum.check, role:admin|manager", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController@setVerify", "as": "admin.", "namespace": null, "prefix": "api/v1/dashboard/admin", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php&line=302\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php:302-314</a>"}, "queries": {"nb_statements": 25, "nb_failed_statements": 0, "accumulated_duration": 0.053720000000000004, "accumulated_duration_str": "53.72ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.02162, "duration_str": "21.62ms", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 0, "width_percent": 40.246}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 40.246, "width_percent": 0.614}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 40.86, "width_percent": 0.614}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 41.474, "width_percent": 0.596}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '55' limit 1", "type": "query", "params": [], "bindings": ["55"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 42.07, "width_percent": 0.949}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 43.019, "width_percent": 0.8}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-22 12:24:20', `personal_access_tokens`.`updated_at` = '2025-07-22 12:24:20' where `id` = 55", "type": "query", "params": [], "bindings": ["2025-07-22 12:24:20", "2025-07-22 12:24:20", "55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 43.82, "width_percent": 2.532}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (101) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 23, "namespace": "middleware", "name": "role", "line": 29}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 18}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 46.351, "width_percent": 0.707}, {"sql": "select * from `shops` where `uuid` = '61f7e2a5-ab4b-400e-aa62-a6d2829c0e51' and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["61f7e2a5-ab4b-400e-aa62-a6d2829c0e51"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 269}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Services\\ShopServices\\ShopService.php:269", "connection": "foodyman", "start_percent": 47.059, "width_percent": 1.303}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'shops'", "type": "query", "params": [], "bindings": ["foodyman", "shops"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Services\\ShopServices\\ShopService.php:283", "connection": "foodyman", "start_percent": 48.362, "width_percent": 2.215}, {"sql": "update `shops` set `verify` = 1, `shops`.`updated_at` = '2025-07-22 12:24:20' where `id` = 503", "type": "query", "params": [], "bindings": ["1", "2025-07-22 12:24:20", "503"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.013779999999999999, "duration_str": "13.78ms", "stmt_id": "\\app\\Services\\ShopServices\\ShopService.php:283", "connection": "foodyman", "start_percent": 50.577, "width_percent": 25.652}, {"sql": "select * from `users` where `users`.`id` = 110 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["110"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 69}, {"index": 27, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Observers\\ShopObserver.php:69", "connection": "foodyman", "start_percent": 76.229, "width_percent": 1.638}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (110) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 69}, {"index": 29, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 30, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 77.867, "width_percent": 1.08}, {"sql": "delete from `model_has_roles` where `model_has_roles`.`model_id` = 110 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["110", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 176}, {"index": 12, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 70}, {"index": 19, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:176", "connection": "foodyman", "start_percent": 78.946, "width_percent": 2.383}, {"sql": "select * from `roles` where `name` = 'seller' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["seller", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 165}, {"index": 16, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 324}, {"index": 18, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 111}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php", "line": 768}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:165", "connection": "foodyman", "start_percent": 81.329, "width_percent": 1.359}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 110 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["110", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 127}, {"index": 16, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 178}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 70}, {"index": 24, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:127", "connection": "foodyman", "start_percent": 82.688, "width_percent": 0.8}, {"sql": "insert into `model_has_roles` (`model_id`, `model_type`, `role_id`) values (110, 'App\\Models\\User', 11)", "type": "query", "params": [], "bindings": ["110", "App\\Models\\User", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 127}, {"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 178}, {"index": 15, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 70}, {"index": 22, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:127", "connection": "foodyman", "start_percent": 83.488, "width_percent": 2.197}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (110) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 128}, {"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 178}, {"index": 20, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 70}, {"index": 27, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:128", "connection": "foodyman", "start_percent": 85.685, "width_percent": 0.689}, {"sql": "update `invitations` set `deleted_at` = '2025-07-22 12:24:20', `invitations`.`updated_at` = '2025-07-22 12:24:20' where `invitations`.`user_id` = 110 and `invitations`.`user_id` is not null and `invitations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-22 12:24:20", "2025-07-22 12:24:20", "110"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 73}, {"index": 24, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Observers\\ShopObserver.php:73", "connection": "foodyman", "start_percent": 86.374, "width_percent": 2.569}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 88.943, "width_percent": 0.949}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 89.892, "width_percent": 0.8}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'model_logs'", "type": "query", "params": [], "bindings": ["foodyman", "model_logs"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 21, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 84}, {"index": 28, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 90.692, "width_percent": 2.55}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Shop', 503, '{\\\"location\\\":\\\"{\\\\\"latitude\\\\\":\\\\\"-18.***************\\\\\",\\\\\"longitude\\\\\":\\\\\"-50.***************\\\\\"}\\\",\\\"delivery_time\\\":\\\"{\\\\\"from\\\\\":\\\\\"45\\\\\",\\\\\"to\\\\\":\\\\\"60\\\\\",\\\\\"type\\\\\":\\\\\"minute\\\\\"}\\\",\\\"verify\\\":0}', 'shop_updated', '2025-07-22 12:24:21', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "503", "{&quot;location&quot;:&quot;{\\&quot;latitude\\&quot;:\\&quot;-18.***************\\&quot;,\\&quot;longitude\\&quot;:\\&quot;-50.***************\\&quot;}&quot;,&quot;delivery_time&quot;:&quot;{\\&quot;from\\&quot;:\\&quot;45\\&quot;,\\&quot;to\\&quot;:\\&quot;60\\&quot;,\\&quot;type\\&quot;:\\&quot;minute\\&quot;}&quot;,&quot;verify&quot;:0}", "shop_updated", "2025-07-22 12:24:21", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\ShopObserver.php", "line": 84}, {"index": 29, "namespace": null, "name": "\\app\\Services\\ShopServices\\ShopService.php", "line": 283}, {"index": 30, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\ShopController.php", "line": 304}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00271, "duration_str": "2.71ms", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 93.243, "width_percent": 5.045}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 98.287, "width_percent": 1.024}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 503 and `orders`.`shop_id` is not null and (`shop_id` = 503 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["503", "503", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 99.311, "width_percent": 0.689}]}, "models": {"data": {"App\\Models\\Shop": 1, "Spatie\\Permission\\Models\\Role": 4, "App\\Models\\User": 2, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 3, "App\\Models\\Language": 3}, "count": 14}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f73d192-c85e-4cd5-b5b8-6b7678ee2484\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/admin/shops/61f7e2a5-ab4b-400e-aa62-a6d2829c0e51/verify", "status_code": "<pre class=sf-dump id=sf-dump-92581326 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-92581326\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-634437164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-634437164\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2010806931 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2010806931\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1515332330 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515332330\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2007083079 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54827</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"73 characters\">/api/v1/dashboard/admin/shops/61f7e2a5-ab4b-400e-aa62-a6d2829c0e51/verify</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"73 characters\">/api/v1/dashboard/admin/shops/61f7e2a5-ab4b-400e-aa62-a6d2829c0e51/verify</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"83 characters\">/index.php/api/v1/dashboard/admin/shops/61f7e2a5-ab4b-400e-aa62-a6d2829c0e51/verify</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753197860.5749</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753197860</span>\n  \"<span class=sf-dump-key>argv</span>\" => []\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007083079\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1343656479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1343656479\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1258643611 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 15:24:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258643611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1143205346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143205346\", {\"maxDepth\":0})</script>\n"}}