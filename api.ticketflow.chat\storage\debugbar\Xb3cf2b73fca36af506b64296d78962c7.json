{"__meta": {"id": "Xb3cf2b73fca36af506b64296d78962c7", "datetime": "2025-07-22 12:25:48", "utime": 1753197948.575371, "method": "POST", "uri": "/api/v1/dashboard/admin/brands?title=Lux&images[]=%7B%22uid%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22name%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22status%22:%22done%22,%22url%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22created%22:true%7D&active=1&images[0]=http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:25:48] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753197948.229478, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753197947.988094, "end": 1753197948.575387, "duration": 0.5872929096221924, "duration_str": "587ms", "measures": [{"label": "Booting", "start": 1753197947.988094, "relative_start": 0, "end": 1753197948.206407, "relative_end": 1753197948.206407, "duration": 0.21831297874450684, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753197948.20642, "relative_start": 0.21832585334777832, "end": 1753197948.575389, "relative_end": 1.9073486328125e-06, "duration": 0.3689689636230469, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43433888, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/admin/brands", "middleware": "api, block.ip, sanctum.check, role:admin|manager", "as": "admin.brands.store", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController@store", "namespace": null, "prefix": "api/v1/dashboard/admin", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php&line=68\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php:68-80</a>"}, "queries": {"nb_statements": 20, "nb_failed_statements": 0, "accumulated_duration": 0.03774, "accumulated_duration_str": "37.74ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.02336, "duration_str": "23.36ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 61.897}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 61.897, "width_percent": 1.325}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 63.222, "width_percent": 0.821}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 64.043, "width_percent": 0.768}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '55' limit 1", "type": "query", "params": [], "bindings": ["55"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 64.812, "width_percent": 1.404}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 66.216, "width_percent": 1.113}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-22 12:25:48', `personal_access_tokens`.`updated_at` = '2025-07-22 12:25:48' where `id` = 55", "type": "query", "params": [], "bindings": ["2025-07-22 12:25:48", "2025-07-22 12:25:48", "55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 67.329, "width_percent": 3.736}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (101) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 23, "namespace": "middleware", "name": "role", "line": 29}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 18}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 71.065, "width_percent": 1.06}, {"sql": "select count(*) as aggregate from `brands` where `title` = 'Lux'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 23}, {"index": 15, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 31}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Services\\BrandService\\BrandService.php:23", "connection": "foodyman", "start_percent": 72.125, "width_percent": 1.802}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'brands'", "type": "query", "params": [], "bindings": ["foodyman", "brands"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\app\\Services\\BrandService\\BrandService.php:34", "connection": "foodyman", "start_percent": 73.927, "width_percent": 3.498}, {"sql": "insert into `brands` (`active`, `title`, `slug`, `uuid`, `updated_at`, `created_at`) values ('1', 'Lux', 'lux-0', '\\\"ebbf3233-0a03-43f4-aa5e-415c351baa2d\\\"', '2025-07-22 12:25:48', '2025-07-22 12:25:48')", "type": "query", "params": [], "bindings": ["1", "<PERSON><PERSON>", "lux-0", "&quot;ebbf3233-0a03-43f4-aa5e-415c351baa2d&quot;", "2025-07-22 12:25:48", "2025-07-22 12:25:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 34}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Services\\BrandService\\BrandService.php:34", "connection": "foodyman", "start_percent": 77.424, "width_percent": 3.71}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 31}, {"index": 28, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 34}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 81.134, "width_percent": 1.033}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 31}, {"index": 28, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 34}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 82.167, "width_percent": 0.954}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'model_logs'", "type": "query", "params": [], "bindings": ["foodyman", "model_logs"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 21, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 31}, {"index": 32, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 34}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 83.121, "width_percent": 2.226}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Brand', 1, '{\\\"active\\\":\\\"1\\\",\\\"title\\\":\\\"Lux\\\",\\\"slug\\\":\\\"lux-0\\\",\\\"uuid\\\":\\\"ebbf3233-0a03-43f4-aa5e-415c351baa2d\\\",\\\"updated_at\\\":\\\"2025-07-22 12:25:48\\\",\\\"created_at\\\":\\\"2025-07-22 12:25:48\\\",\\\"id\\\":1}', 'brand_created', '2025-07-22 12:25:48', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Brand", "1", "{&quot;active&quot;:&quot;1&quot;,&quot;title&quot;:&quot;Lux&quot;,&quot;slug&quot;:&quot;lux-0&quot;,&quot;uuid&quot;:&quot;ebbf3233-0a03-43f4-aa5e-415c351baa2d&quot;,&quot;updated_at&quot;:&quot;2025-07-22 12:25:48&quot;,&quot;created_at&quot;:&quot;2025-07-22 12:25:48&quot;,&quot;id&quot;:1}", "brand_created", "2025-07-22 12:25:48", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 31}, {"index": 33, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 34}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 85.347, "width_percent": 3.074}, {"sql": "update `brands` set `img` = 'http://localhost:8000/storage/images/brands/101-**********.webp', `brands`.`updated_at` = '2025-07-22 12:25:48' where `id` = 1", "type": "query", "params": [], "bindings": ["http://localhost:8000/storage/images/brands/101-**********.webp", "2025-07-22 12:25:48", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 42}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Services\\BrandService\\BrandService.php:42", "connection": "foodyman", "start_percent": 88.421, "width_percent": 3.71}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 42}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 92.13, "width_percent": 0.742}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 42}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 92.872, "width_percent": 0.795}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Brand', 1, '{\\\"uuid\\\":\\\"ebbf3233-0a03-43f4-aa5e-415c351baa2d\\\"}', 'brand_updated', '2025-07-22 12:25:48', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Brand", "1", "{&quot;uuid&quot;:&quot;ebbf3233-0a03-43f4-aa5e-415c351baa2d&quot;}", "brand_updated", "2025-07-22 12:25:48", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\BrandObserver.php", "line": 42}, {"index": 29, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 42}, {"index": 30, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 93.667, "width_percent": 2.385}, {"sql": "insert into `galleries` (`title`, `path`, `type`, `size`, `mime`, `loadable_id`, `loadable_type`) values ('\\\"101-**********.webp\\\"', 'http://localhost:8000/storage/images/brands/101-**********.webp', '', '', '', 1, 'App\\Models\\Brand')", "type": "query", "params": [], "bindings": ["&quot;101-**********.webp&quot;", "http://localhost:8000/storage/images/brands/101-**********.webp", "", "", "", "1", "App\\Models\\Brand"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Traits\\Loadable.php", "line": 34}, {"index": 17, "namespace": null, "name": "\\app\\Services\\BrandService\\BrandService.php", "line": 46}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\BrandController.php", "line": 70}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\app\\Traits\\Loadable.php:34", "connection": "foodyman", "start_percent": 96.052, "width_percent": 3.948}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": 1, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 4, "App\\Models\\Language": 4}, "count": 11}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f73d218-37a9-44d4-8d23-cea15c6c5576\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/admin/brands", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-86778537 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Lux</span>\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://localhost:8000/storage/images/brands/101-**********.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>active</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86778537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1871118812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1871118812\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1638390609 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1638390609\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1401121345 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54966</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"470 characters\">/api/v1/dashboard/admin/brands?title=Lux&amp;images[]=%7B%22uid%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22name%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22status%22:%22done%22,%22url%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22created%22:true%7D&amp;active=1&amp;images[0]=http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/api/v1/dashboard/admin/brands</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/index.php/api/v1/dashboard/admin/brands</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"439 characters\">title=Lux&amp;images[]=%7B%22uid%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22name%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22status%22:%22done%22,%22url%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22created%22:true%7D&amp;active=1&amp;images[0]=http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 55|urpVUd13xlJyYIukxUJK3zTprZgqbTd4fqziaIw1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753197947.9881</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753197947</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"439 characters\">title=Lux&amp;images[]=%7B%22uid%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22name%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22status%22:%22done%22,%22url%22:%22http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp%22,%22created%22:true%7D&amp;active=1&amp;images[0]=http:%2F%2Flocalhost:8000%2Fstorage%2Fimages%2Fbrands%2F101-**********.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401121345\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-684737732 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-684737732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-877135047 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 15:25:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4998</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877135047\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1153549181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1153549181\", {\"maxDepth\":0})</script>\n"}}