import React, { useState, useMemo, useEffect, useRef } from 'react';
import debounce from 'lodash/debounce';
import { Select, Spin } from 'antd';

export const InfiniteSelect = ({
  fetchOptions,
  debounceTimeout = 400,
  hasMore,
  refetchOnFocus = false,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const isMountedRef = useRef(true);

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value) => {
      if (!isMountedRef.current) return;

      setOptions([]);
      setSearch(value);
      setFetching(true);
      fetchOptions({ search: value })
        .then((newOptions) => {
          if (isMountedRef.current) {
            setOptions(newOptions);
            setCurrentPage(2);
            setFetching(false);
          }
        })
        .finally(() => {
          if (isMountedRef.current) {
            setLoading(false);
          }
        });
    };
    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout, currentPage]);

  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
      if (debounceFetcher && typeof debounceFetcher.cancel === 'function') {
        debounceFetcher.cancel();
      }
    };
  }, []); // Empty dependency array to prevent recreation

  const fetchOnFocus = () => {
    if (refetchOnFocus) {
      debounceFetcher('');
    } else {
      if (!options?.length) {
        debounceFetcher('');
      }
    }
  };

  const onScroll = async (event) => {
    const target = event.target;
    if (
      !loading &&
      target.scrollTop + target.offsetHeight === target.scrollHeight
    ) {
      if (hasMore && isMountedRef.current) {
        setLoading(true);
        // target.scrollTo(0, target.scrollHeight);
        fetchOptions({ search: search, page: currentPage })
          .then((item) => {
            if (isMountedRef.current) {
              setCurrentPage((i) => i + 1);
              setOptions([...options, ...item]);
            }
          })
          .finally(() => {
            if (isMountedRef.current) {
              setLoading(false);
            }
          });
      }
    }
  };

  return (
    <Select
      showSearch
      allowClear
      onPopupScroll={onScroll}
      labelInValue={true}
      filterOption={false}
      onSearch={debounceFetcher}
      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}
      onFocus={fetchOnFocus}
      {...props}
    >
      {options.map((item, index) => (
        <Select.Option key={index} value={item.value}>
          {item.label}
        </Select.Option>
      ))}
      {loading && (
        <Select.Option>
          <Spin size='small' />
        </Select.Option>
      )}
    </Select>
  );
};
