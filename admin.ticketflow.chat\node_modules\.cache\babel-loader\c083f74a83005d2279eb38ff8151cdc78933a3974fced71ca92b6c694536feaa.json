{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\search.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const DebounceSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  onClear,\n  ...props\n}) => {\n  _s();\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const isMountedRef = useRef(true);\n  const debounceFetcherRef = useRef(null);\n\n  // Stable callback that doesn't change on every render\n  const loadOptions = useCallback(value => {\n    if (!isMountedRef.current) return;\n    setOptions([]);\n    setFetching(true);\n    fetchOptions(value).then(newOptions => {\n      if (isMountedRef.current) {\n        setOptions(newOptions || []);\n        setFetching(false);\n      }\n    }).catch(error => {\n      if (isMountedRef.current) {\n        console.error('DebounceSelect fetch error:', error);\n        setOptions([]);\n        setFetching(false);\n      }\n    });\n  }, [fetchOptions]);\n\n  // Create debounced function and store in ref\n  const debounceFetcher = useMemo(() => {\n    const debouncedFn = debounce(loadOptions, debounceTimeout);\n    debounceFetcherRef.current = debouncedFn;\n    return debouncedFn;\n  }, [loadOptions, debounceTimeout]);\n\n  // Cleanup effect that runs only once\n  useEffect(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n      // Cancel any pending debounced calls using the ref\n      if (debounceFetcherRef.current) {\n        debounceFetcherRef.current.cancel();\n      }\n    };\n  }, []); // Empty dependency array - runs only on mount/unmount\n\n  const fetchOnFocus = useCallback(() => {\n    if (isMountedRef.current) {\n      debounceFetcher('');\n    }\n  }, [debounceFetcher]);\n  const handleClear = useCallback(() => {\n    if (isMountedRef.current) {\n      debounceFetcher('');\n      if (onClear) {\n        onClear();\n      }\n    }\n  }, [debounceFetcher, onClear]);\n  const handleSearch = useCallback(value => {\n    if (isMountedRef.current) {\n      debounceFetcher(value);\n    }\n  }, [debounceFetcher]);\n  return /*#__PURE__*/_jsxDEV(Select, {\n    showSearch: true,\n    allowClear: true,\n    labelInValue: true,\n    filterOption: false,\n    onSearch: handleSearch,\n    onClear: handleClear,\n    notFoundContent: fetching ? /*#__PURE__*/_jsxDEV(Spin, {\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 35\n    }, this) : 'no results',\n    ...props,\n    options: options,\n    onFocus: fetchOnFocus\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(DebounceSelect, \"fs2aBPm2h/xcph3aFUidFDZuWCc=\");\n_c = DebounceSelect;\nvar _c;\n$RefreshReg$(_c, \"DebounceSelect\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "useEffect", "useRef", "useCallback", "debounce", "Select", "Spin", "jsxDEV", "_jsxDEV", "DebounceSelect", "fetchOptions", "debounceTimeout", "onClear", "props", "_s", "fetching", "setFetching", "options", "setOptions", "isMountedRef", "debounceFetcherRef", "loadOptions", "value", "current", "then", "newOptions", "catch", "error", "console", "deboun<PERSON><PERSON><PERSON><PERSON>", "debouncedFn", "cancel", "fetchOnFocus", "handleClear", "handleSearch", "showSearch", "allowClear", "labelInValue", "filterOption", "onSearch", "notFoundContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onFocus", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/search.js"], "sourcesContent": ["import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';\nimport debounce from 'lodash/debounce';\nimport { Select, Spin } from 'antd';\n\nexport const DebounceSelect = ({\n  fetchOptions,\n  debounceTimeout = 400,\n  onClear,\n  ...props\n}) => {\n  const [fetching, setFetching] = useState(false);\n  const [options, setOptions] = useState([]);\n  const isMountedRef = useRef(true);\n  const debounceFetcherRef = useRef(null);\n\n  // Stable callback that doesn't change on every render\n  const loadOptions = useCallback((value) => {\n    if (!isMountedRef.current) return;\n\n    setOptions([]);\n    setFetching(true);\n\n    fetchOptions(value)\n      .then((newOptions) => {\n        if (isMountedRef.current) {\n          setOptions(newOptions || []);\n          setFetching(false);\n        }\n      })\n      .catch((error) => {\n        if (isMountedRef.current) {\n          console.error('DebounceSelect fetch error:', error);\n          setOptions([]);\n          setFetching(false);\n        }\n      });\n  }, [fetchOptions]);\n\n  // Create debounced function and store in ref\n  const debounceFetcher = useMemo(() => {\n    const debouncedFn = debounce(loadOptions, debounceTimeout);\n    debounceFetcherRef.current = debouncedFn;\n    return debouncedFn;\n  }, [loadOptions, debounceTimeout]);\n\n  // Cleanup effect that runs only once\n  useEffect(() => {\n    isMountedRef.current = true;\n\n    return () => {\n      isMountedRef.current = false;\n      // Cancel any pending debounced calls using the ref\n      if (debounceFetcherRef.current) {\n        debounceFetcherRef.current.cancel();\n      }\n    };\n  }, []); // Empty dependency array - runs only on mount/unmount\n\n  const fetchOnFocus = useCallback(() => {\n    if (isMountedRef.current) {\n      debounceFetcher('');\n    }\n  }, [debounceFetcher]);\n\n  const handleClear = useCallback(() => {\n    if (isMountedRef.current) {\n      debounceFetcher('');\n      if (onClear) {\n        onClear();\n      }\n    }\n  }, [debounceFetcher, onClear]);\n\n  const handleSearch = useCallback((value) => {\n    if (isMountedRef.current) {\n      debounceFetcher(value);\n    }\n  }, [debounceFetcher]);\n\n  return (\n    <Select\n      showSearch\n      allowClear\n      labelInValue={true}\n      filterOption={false}\n      onSearch={handleSearch}\n      onClear={handleClear}\n      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}\n      {...props}\n      options={options}\n      onFocus={fetchOnFocus}\n    />\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChF,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,YAAY;EACZC,eAAe,GAAG,GAAG;EACrBC,OAAO;EACP,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMoB,YAAY,GAAGjB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkB,kBAAkB,GAAGlB,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMmB,WAAW,GAAGlB,WAAW,CAAEmB,KAAK,IAAK;IACzC,IAAI,CAACH,YAAY,CAACI,OAAO,EAAE;IAE3BL,UAAU,CAAC,EAAE,CAAC;IACdF,WAAW,CAAC,IAAI,CAAC;IAEjBN,YAAY,CAACY,KAAK,CAAC,CAChBE,IAAI,CAAEC,UAAU,IAAK;MACpB,IAAIN,YAAY,CAACI,OAAO,EAAE;QACxBL,UAAU,CAACO,UAAU,IAAI,EAAE,CAAC;QAC5BT,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC,CACDU,KAAK,CAAEC,KAAK,IAAK;MAChB,IAAIR,YAAY,CAACI,OAAO,EAAE;QACxBK,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDT,UAAU,CAAC,EAAE,CAAC;QACdF,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;EACN,CAAC,EAAE,CAACN,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMmB,eAAe,GAAG7B,OAAO,CAAC,MAAM;IACpC,MAAM8B,WAAW,GAAG1B,QAAQ,CAACiB,WAAW,EAAEV,eAAe,CAAC;IAC1DS,kBAAkB,CAACG,OAAO,GAAGO,WAAW;IACxC,OAAOA,WAAW;EACpB,CAAC,EAAE,CAACT,WAAW,EAAEV,eAAe,CAAC,CAAC;;EAElC;EACAV,SAAS,CAAC,MAAM;IACdkB,YAAY,CAACI,OAAO,GAAG,IAAI;IAE3B,OAAO,MAAM;MACXJ,YAAY,CAACI,OAAO,GAAG,KAAK;MAC5B;MACA,IAAIH,kBAAkB,CAACG,OAAO,EAAE;QAC9BH,kBAAkB,CAACG,OAAO,CAACQ,MAAM,CAAC,CAAC;MACrC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMC,YAAY,GAAG7B,WAAW,CAAC,MAAM;IACrC,IAAIgB,YAAY,CAACI,OAAO,EAAE;MACxBM,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMI,WAAW,GAAG9B,WAAW,CAAC,MAAM;IACpC,IAAIgB,YAAY,CAACI,OAAO,EAAE;MACxBM,eAAe,CAAC,EAAE,CAAC;MACnB,IAAIjB,OAAO,EAAE;QACXA,OAAO,CAAC,CAAC;MACX;IACF;EACF,CAAC,EAAE,CAACiB,eAAe,EAAEjB,OAAO,CAAC,CAAC;EAE9B,MAAMsB,YAAY,GAAG/B,WAAW,CAAEmB,KAAK,IAAK;IAC1C,IAAIH,YAAY,CAACI,OAAO,EAAE;MACxBM,eAAe,CAACP,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACO,eAAe,CAAC,CAAC;EAErB,oBACErB,OAAA,CAACH,MAAM;IACL8B,UAAU;IACVC,UAAU;IACVC,YAAY,EAAE,IAAK;IACnBC,YAAY,EAAE,KAAM;IACpBC,QAAQ,EAAEL,YAAa;IACvBtB,OAAO,EAAEqB,WAAY;IACrBO,eAAe,EAAEzB,QAAQ,gBAAGP,OAAA,CAACF,IAAI;MAACmC,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,YAAa;IAAA,GAC7DhC,KAAK;IACTI,OAAO,EAAEA,OAAQ;IACjB6B,OAAO,EAAEd;EAAa;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEN,CAAC;AAAC/B,EAAA,CAzFWL,cAAc;AAAAsC,EAAA,GAAdtC,cAAc;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}