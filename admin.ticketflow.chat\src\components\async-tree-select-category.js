import React, { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import { Spin, TreeSelect } from 'antd';
import debounce from 'lodash/debounce';

export const AsyncTreeSelect = ({
  fetchOptions,
  refetch = false,
  debounceTimeout = 400,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [treeData, setTreeData] = useState([]);
  const isMountedRef = useRef(true);
  const debounceFetcherRef = useRef(null);

  const fetchOnFocus = useCallback(() => {
    if (!isMountedRef.current) return;

    if (!treeData.length || refetch) {
      setFetching(true);
      fetchOptions()
        .then((newOptions) => {
          if (isMountedRef.current) {
            setTreeData(newOptions || []);
            setFetching(false);
          }
        })
        .catch((error) => {
          if (isMountedRef.current) {
            console.error('AsyncTreeSelect fetch error:', error);
            setTreeData([]);
            setFetching(false);
          }
        });
    }
  }, [treeData.length, refetch, fetchOptions]);

  const loadOptions = useCallback((value) => {
    if (!isMountedRef.current) return;

    setTreeData([]);
    setFetching(true);
    fetchOptions(value)
      .then((newOptions) => {
        if (isMountedRef.current) {
          setTreeData(newOptions || []);
          setFetching(false);
        }
      })
      .catch((error) => {
        if (isMountedRef.current) {
          console.error('AsyncTreeSelect search error:', error);
          setTreeData([]);
          setFetching(false);
        }
      });
  }, [fetchOptions]);

  const debounceFetcher = useMemo(() => {
    const debouncedFn = debounce(loadOptions, debounceTimeout);
    debounceFetcherRef.current = debouncedFn;
    return debouncedFn;
  }, [loadOptions, debounceTimeout]);

  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
      if (debounceFetcherRef.current) {
        debounceFetcherRef.current.cancel();
      }
    };
  }, []);

  return (
    <TreeSelect
      showSearch
      labelInValue
      filterTreeNode={(value, node) => {
        if (node) {
          return node?.label?.localeCompare(value);
        }
      }}
      treeLine={true}
      onSearch={(value) => {
        console.log('value', value);
        debounceFetcher(value);
      }}
      filterOption={false}
      treeData={fetching ? [] : treeData}
      treeDefaultExpandAll
      onFocus={fetchOnFocus}
      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}
      {...props}
    />
  );
};
