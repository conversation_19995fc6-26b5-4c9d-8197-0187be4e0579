import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import debounce from 'lodash/debounce';
import { Select, Spin } from 'antd';

export const DebounceSelect = ({
  fetchOptions,
  debounceTimeout = 400,
  onClear,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const isMountedRef = useRef(true);
  const debounceFetcherRef = useRef(null);

  // Stable callback that doesn't change on every render
  const loadOptions = useCallback((value) => {
    if (!isMountedRef.current) return;

    setOptions([]);
    setFetching(true);

    fetchOptions(value)
      .then((newOptions) => {
        if (isMountedRef.current) {
          setOptions(newOptions || []);
          setFetching(false);
        }
      })
      .catch((error) => {
        if (isMountedRef.current) {
          console.error('DebounceSelect fetch error:', error);
          setOptions([]);
          setFetching(false);
        }
      });
  }, [fetchOptions]);

  // Create debounced function and store in ref
  const debounceFetcher = useMemo(() => {
    const debouncedFn = debounce(loadOptions, debounceTimeout);
    debounceFetcherRef.current = debouncedFn;
    return debouncedFn;
  }, [loadOptions, debounceTimeout]);

  // Cleanup effect that runs only once
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
      // Cancel any pending debounced calls using the ref
      if (debounceFetcherRef.current) {
        debounceFetcherRef.current.cancel();
      }
    };
  }, []); // Empty dependency array - runs only on mount/unmount

  const fetchOnFocus = useCallback(() => {
    if (isMountedRef.current) {
      debounceFetcher('');
    }
  }, [debounceFetcher]);

  const handleClear = useCallback(() => {
    if (isMountedRef.current) {
      debounceFetcher('');
      if (onClear) {
        onClear();
      }
    }
  }, [debounceFetcher, onClear]);

  const handleSearch = useCallback((value) => {
    if (isMountedRef.current) {
      debounceFetcher(value);
    }
  }, [debounceFetcher]);

  return (
    <Select
      showSearch
      allowClear
      labelInValue={true}
      filterOption={false}
      onSearch={handleSearch}
      onClear={handleClear}
      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}
      {...props}
      options={options}
      onFocus={fetchOnFocus}
    />
  );
};
