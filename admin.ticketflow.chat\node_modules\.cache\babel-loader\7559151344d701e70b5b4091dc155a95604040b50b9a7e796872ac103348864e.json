{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\async-tree-select-category.js\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState, useEffect, useRef, useCallback } from 'react';\nimport { Spin, TreeSelect } from 'antd';\nimport debounce from 'lodash/debounce';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AsyncTreeSelect = ({\n  fetchOptions,\n  refetch = false,\n  debounceTimeout = 400,\n  ...props\n}) => {\n  _s();\n  const [fetching, setFetching] = useState(false);\n  const [treeData, setTreeData] = useState([]);\n  const isMountedRef = useRef(true);\n  const debounceFetcherRef = useRef(null);\n  const fetchOnFocus = useCallback(() => {\n    if (!isMountedRef.current) return;\n    if (!treeData.length || refetch) {\n      setFetching(true);\n      fetchOptions().then(newOptions => {\n        if (isMountedRef.current) {\n          setTreeData(newOptions || []);\n          setFetching(false);\n        }\n      }).catch(error => {\n        if (isMountedRef.current) {\n          console.error('AsyncTreeSelect fetch error:', error);\n          setTreeData([]);\n          setFetching(false);\n        }\n      });\n    }\n  }, [treeData.length, refetch, fetchOptions]);\n  const loadOptions = useCallback(value => {\n    if (!isMountedRef.current) return;\n    setTreeData([]);\n    setFetching(true);\n    fetchOptions(value).then(newOptions => {\n      if (isMountedRef.current) {\n        setTreeData(newOptions || []);\n        setFetching(false);\n      }\n    }).catch(error => {\n      if (isMountedRef.current) {\n        console.error('AsyncTreeSelect search error:', error);\n        setTreeData([]);\n        setFetching(false);\n      }\n    });\n  }, [fetchOptions]);\n  const debounceFetcher = useMemo(() => {\n    const debouncedFn = debounce(loadOptions, debounceTimeout);\n    debounceFetcherRef.current = debouncedFn;\n    return debouncedFn;\n  }, [loadOptions, debounceTimeout]);\n  useEffect(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n      if (debounceFetcherRef.current) {\n        debounceFetcherRef.current.cancel();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(TreeSelect, {\n    showSearch: true,\n    labelInValue: true,\n    filterTreeNode: (value, node) => {\n      if (node) {\n        var _node$label;\n        return node === null || node === void 0 ? void 0 : (_node$label = node.label) === null || _node$label === void 0 ? void 0 : _node$label.localeCompare(value);\n      }\n    },\n    treeLine: true,\n    onSearch: value => {\n      console.log('value', value);\n      debounceFetcher(value);\n    },\n    filterOption: false,\n    treeData: fetching ? [] : treeData,\n    treeDefaultExpandAll: true,\n    onFocus: fetchOnFocus,\n    notFoundContent: fetching ? /*#__PURE__*/_jsxDEV(Spin, {\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 35\n    }, this) : 'no results',\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(AsyncTreeSelect, \"GF3CIjxrD2WrNNYxIhTk/DVF8Rs=\");\n_c = AsyncTreeSelect;\nvar _c;\n$RefreshReg$(_c, \"AsyncTreeSelect\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useEffect", "useRef", "useCallback", "Spin", "TreeSelect", "debounce", "jsxDEV", "_jsxDEV", "AsyncTreeSelect", "fetchOptions", "refetch", "debounceTimeout", "props", "_s", "fetching", "setFetching", "treeData", "setTreeData", "isMountedRef", "debounceFetcherRef", "fetchOnFocus", "current", "length", "then", "newOptions", "catch", "error", "console", "loadOptions", "value", "deboun<PERSON><PERSON><PERSON><PERSON>", "debouncedFn", "cancel", "showSearch", "labelInValue", "filterTreeNode", "node", "_node$label", "label", "localeCompare", "treeLine", "onSearch", "log", "filterOption", "treeDefaultExpandAll", "onFocus", "notFoundContent", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/async-tree-select-category.js"], "sourcesContent": ["import React, { useMemo, useState, useEffect, useRef, useCallback } from 'react';\nimport { Spin, TreeSelect } from 'antd';\nimport debounce from 'lodash/debounce';\n\nexport const AsyncTreeSelect = ({\n  fetchOptions,\n  refetch = false,\n  debounceTimeout = 400,\n  ...props\n}) => {\n  const [fetching, setFetching] = useState(false);\n  const [treeData, setTreeData] = useState([]);\n  const isMountedRef = useRef(true);\n  const debounceFetcherRef = useRef(null);\n\n  const fetchOnFocus = useCallback(() => {\n    if (!isMountedRef.current) return;\n\n    if (!treeData.length || refetch) {\n      setFetching(true);\n      fetchOptions()\n        .then((newOptions) => {\n          if (isMountedRef.current) {\n            setTreeData(newOptions || []);\n            setFetching(false);\n          }\n        })\n        .catch((error) => {\n          if (isMountedRef.current) {\n            console.error('AsyncTreeSelect fetch error:', error);\n            setTreeData([]);\n            setFetching(false);\n          }\n        });\n    }\n  }, [treeData.length, refetch, fetchOptions]);\n\n  const loadOptions = useCallback((value) => {\n    if (!isMountedRef.current) return;\n\n    setTreeData([]);\n    setFetching(true);\n    fetchOptions(value)\n      .then((newOptions) => {\n        if (isMountedRef.current) {\n          setTreeData(newOptions || []);\n          setFetching(false);\n        }\n      })\n      .catch((error) => {\n        if (isMountedRef.current) {\n          console.error('AsyncTreeSelect search error:', error);\n          setTreeData([]);\n          setFetching(false);\n        }\n      });\n  }, [fetchOptions]);\n\n  const debounceFetcher = useMemo(() => {\n    const debouncedFn = debounce(loadOptions, debounceTimeout);\n    debounceFetcherRef.current = debouncedFn;\n    return debouncedFn;\n  }, [loadOptions, debounceTimeout]);\n\n  useEffect(() => {\n    isMountedRef.current = true;\n\n    return () => {\n      isMountedRef.current = false;\n      if (debounceFetcherRef.current) {\n        debounceFetcherRef.current.cancel();\n      }\n    };\n  }, []);\n\n  return (\n    <TreeSelect\n      showSearch\n      labelInValue\n      filterTreeNode={(value, node) => {\n        if (node) {\n          return node?.label?.localeCompare(value);\n        }\n      }}\n      treeLine={true}\n      onSearch={(value) => {\n        console.log('value', value);\n        debounceFetcher(value);\n      }}\n      filterOption={false}\n      treeData={fetching ? [] : treeData}\n      treeDefaultExpandAll\n      onFocus={fetchOnFocus}\n      notFoundContent={fetching ? <Spin size='small' /> : 'no results'}\n      {...props}\n    />\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChF,SAASC,IAAI,EAAEC,UAAU,QAAQ,MAAM;AACvC,OAAOC,QAAQ,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAC9BC,YAAY;EACZC,OAAO,GAAG,KAAK;EACfC,eAAe,GAAG,GAAG;EACrB,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMmB,YAAY,GAAGjB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkB,kBAAkB,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAEvC,MAAMmB,YAAY,GAAGlB,WAAW,CAAC,MAAM;IACrC,IAAI,CAACgB,YAAY,CAACG,OAAO,EAAE;IAE3B,IAAI,CAACL,QAAQ,CAACM,MAAM,IAAIZ,OAAO,EAAE;MAC/BK,WAAW,CAAC,IAAI,CAAC;MACjBN,YAAY,CAAC,CAAC,CACXc,IAAI,CAAEC,UAAU,IAAK;QACpB,IAAIN,YAAY,CAACG,OAAO,EAAE;UACxBJ,WAAW,CAACO,UAAU,IAAI,EAAE,CAAC;UAC7BT,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC,CACDU,KAAK,CAAEC,KAAK,IAAK;QAChB,IAAIR,YAAY,CAACG,OAAO,EAAE;UACxBM,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpDT,WAAW,CAAC,EAAE,CAAC;UACfF,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC;IACN;EACF,CAAC,EAAE,CAACC,QAAQ,CAACM,MAAM,EAAEZ,OAAO,EAAED,YAAY,CAAC,CAAC;EAE5C,MAAMmB,WAAW,GAAG1B,WAAW,CAAE2B,KAAK,IAAK;IACzC,IAAI,CAACX,YAAY,CAACG,OAAO,EAAE;IAE3BJ,WAAW,CAAC,EAAE,CAAC;IACfF,WAAW,CAAC,IAAI,CAAC;IACjBN,YAAY,CAACoB,KAAK,CAAC,CAChBN,IAAI,CAAEC,UAAU,IAAK;MACpB,IAAIN,YAAY,CAACG,OAAO,EAAE;QACxBJ,WAAW,CAACO,UAAU,IAAI,EAAE,CAAC;QAC7BT,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC,CACDU,KAAK,CAAEC,KAAK,IAAK;MAChB,IAAIR,YAAY,CAACG,OAAO,EAAE;QACxBM,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDT,WAAW,CAAC,EAAE,CAAC;QACfF,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;EACN,CAAC,EAAE,CAACN,YAAY,CAAC,CAAC;EAElB,MAAMqB,eAAe,GAAGhC,OAAO,CAAC,MAAM;IACpC,MAAMiC,WAAW,GAAG1B,QAAQ,CAACuB,WAAW,EAAEjB,eAAe,CAAC;IAC1DQ,kBAAkB,CAACE,OAAO,GAAGU,WAAW;IACxC,OAAOA,WAAW;EACpB,CAAC,EAAE,CAACH,WAAW,EAAEjB,eAAe,CAAC,CAAC;EAElCX,SAAS,CAAC,MAAM;IACdkB,YAAY,CAACG,OAAO,GAAG,IAAI;IAE3B,OAAO,MAAM;MACXH,YAAY,CAACG,OAAO,GAAG,KAAK;MAC5B,IAAIF,kBAAkB,CAACE,OAAO,EAAE;QAC9BF,kBAAkB,CAACE,OAAO,CAACW,MAAM,CAAC,CAAC;MACrC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEzB,OAAA,CAACH,UAAU;IACT6B,UAAU;IACVC,YAAY;IACZC,cAAc,EAAEA,CAACN,KAAK,EAAEO,IAAI,KAAK;MAC/B,IAAIA,IAAI,EAAE;QAAA,IAAAC,WAAA;QACR,OAAOD,IAAI,aAAJA,IAAI,wBAAAC,WAAA,GAAJD,IAAI,CAAEE,KAAK,cAAAD,WAAA,uBAAXA,WAAA,CAAaE,aAAa,CAACV,KAAK,CAAC;MAC1C;IACF,CAAE;IACFW,QAAQ,EAAE,IAAK;IACfC,QAAQ,EAAGZ,KAAK,IAAK;MACnBF,OAAO,CAACe,GAAG,CAAC,OAAO,EAAEb,KAAK,CAAC;MAC3BC,eAAe,CAACD,KAAK,CAAC;IACxB,CAAE;IACFc,YAAY,EAAE,KAAM;IACpB3B,QAAQ,EAAEF,QAAQ,GAAG,EAAE,GAAGE,QAAS;IACnC4B,oBAAoB;IACpBC,OAAO,EAAEzB,YAAa;IACtB0B,eAAe,EAAEhC,QAAQ,gBAAGP,OAAA,CAACJ,IAAI;MAAC4C,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,YAAa;IAAA,GAC7DvC;EAAK;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACtC,EAAA,CA7FWL,eAAe;AAAA4C,EAAA,GAAf5C,eAAe;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}