import React, { useState, useMemo } from 'react';
import {
  Button,
  Dropdown,
  Space,
  Switch,
  Typography,
} from 'antd';
import { TableOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
const { Text } = Typography;

const FilterColumns = ({
  columns = [],
  setColumns,
  style,
  size = '',
  iconOnly,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  const handleVisibleChange = (flag) => {
    setOpen(flag);
  };

  function onChange(checked) {
    const newArray = columns?.map((item) => {
      if (item.dataIndex === checked.dataIndex) {
        item.is_show = !item?.is_show;
      }
      return item;
    });
    setColumns(newArray);
  }

  // Create menu items using the new Ant Design items prop format
  const menuItems = useMemo(() => {
    return columns?.map((item, key) => ({
      key: `${item.dataIndex || item.key || key}`,
      label: (
        <Space className='d-flex justify-content-between' style={{ width: '100%' }}>
          <Text>{item.title}</Text>
          <Switch
            checked={item.is_show}
            onChange={() => onChange(item)}
            disabled={key === 1} // Keep the same disabled logic
            size="small"
          />
        </Space>
      ),
      // Prevent menu item click when clicking the switch
      onClick: (e) => {
        e.domEvent.stopPropagation();
      }
    })) || [];
  }, [columns]);

  return (
    <Dropdown
      menu={{ items: menuItems }}
      trigger={['click']}
      onOpenChange={handleVisibleChange}
      open={open}
      placement="bottomRight"
    >
      <Button
        style={{ ...style }}
        size={size}
        icon={<TableOutlined />}
        title={t('change.columns')} // Use native title instead of Tooltip to avoid findDOMNode
      >
        {iconOnly ? null : t('Columns')}
      </Button>
    </Dropdown>
  );
};

export default FilterColumns;
