{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\filter-column.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Button, Dropdown, Space, Switch, Typography } from 'antd';\nimport { TableOutlined } from '@ant-design/icons';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nconst FilterColumns = ({\n  columns = [],\n  setColumns,\n  style,\n  size = '',\n  iconOnly\n}) => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [open, setOpen] = useState(false);\n  const handleVisibleChange = flag => {\n    setOpen(flag);\n  };\n  function onChange(checked) {\n    const newArray = columns === null || columns === void 0 ? void 0 : columns.map(item => {\n      if (item.dataIndex === checked.dataIndex) {\n        item.is_show = !(item !== null && item !== void 0 && item.is_show);\n      }\n      return item;\n    });\n    setColumns(newArray);\n  }\n\n  // Create menu items using the new Ant Design items prop format\n  const menuItems = useMemo(() => {\n    return (columns === null || columns === void 0 ? void 0 : columns.map((item, key) => ({\n      key: `${item.dataIndex || item.key || key}`,\n      label: /*#__PURE__*/_jsxDEV(Space, {\n        className: \"d-flex justify-content-between\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Switch, {\n          checked: item.is_show,\n          onChange: () => onChange(item),\n          disabled: key === 1 // Keep the same disabled logic\n          ,\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this),\n      // Prevent menu item click when clicking the switch\n      onClick: e => {\n        e.domEvent.stopPropagation();\n      }\n    }))) || [];\n  }, [columns]);\n  return /*#__PURE__*/_jsxDEV(Dropdown, {\n    menu: {\n      items: menuItems\n    },\n    trigger: ['click'],\n    onOpenChange: handleVisibleChange,\n    open: open,\n    placement: \"bottomRight\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      style: {\n        ...style\n      },\n      size: size,\n      icon: /*#__PURE__*/_jsxDEV(TableOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 15\n      }, this),\n      title: t('change.columns') // Use native title instead of Tooltip to avoid findDOMNode\n      ,\n      children: iconOnly ? null : t('Columns')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(FilterColumns, \"kgsPSdh910qsVCEteay45LA78yQ=\", false, function () {\n  return [useTranslation];\n});\n_c = FilterColumns;\nexport default FilterColumns;\nvar _c;\n$RefreshReg$(_c, \"FilterColumns\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "<PERSON><PERSON>", "Dropdown", "Space", "Switch", "Typography", "TableOutlined", "useTranslation", "jsxDEV", "_jsxDEV", "Text", "FilterColumns", "columns", "setColumns", "style", "size", "iconOnly", "_s", "t", "open", "<PERSON><PERSON><PERSON>", "handleVisibleChange", "flag", "onChange", "checked", "newArray", "map", "item", "dataIndex", "is_show", "menuItems", "key", "label", "className", "width", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "onClick", "e", "domEvent", "stopPropagation", "menu", "items", "trigger", "onOpenChange", "placement", "icon", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/filter-column.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport {\n  Button,\n  Dropdown,\n  Space,\n  Switch,\n  Typography,\n} from 'antd';\nimport { TableOutlined } from '@ant-design/icons';\nimport { useTranslation } from 'react-i18next';\nconst { Text } = Typography;\n\nconst FilterColumns = ({\n  columns = [],\n  setColumns,\n  style,\n  size = '',\n  iconOnly,\n}) => {\n  const { t } = useTranslation();\n  const [open, setOpen] = useState(false);\n\n  const handleVisibleChange = (flag) => {\n    setOpen(flag);\n  };\n\n  function onChange(checked) {\n    const newArray = columns?.map((item) => {\n      if (item.dataIndex === checked.dataIndex) {\n        item.is_show = !item?.is_show;\n      }\n      return item;\n    });\n    setColumns(newArray);\n  }\n\n  // Create menu items using the new Ant Design items prop format\n  const menuItems = useMemo(() => {\n    return columns?.map((item, key) => ({\n      key: `${item.dataIndex || item.key || key}`,\n      label: (\n        <Space className='d-flex justify-content-between' style={{ width: '100%' }}>\n          <Text>{item.title}</Text>\n          <Switch\n            checked={item.is_show}\n            onChange={() => onChange(item)}\n            disabled={key === 1} // Keep the same disabled logic\n            size=\"small\"\n          />\n        </Space>\n      ),\n      // Prevent menu item click when clicking the switch\n      onClick: (e) => {\n        e.domEvent.stopPropagation();\n      }\n    })) || [];\n  }, [columns]);\n\n  return (\n    <Dropdown\n      menu={{ items: menuItems }}\n      trigger={['click']}\n      onOpenChange={handleVisibleChange}\n      open={open}\n      placement=\"bottomRight\"\n    >\n      <Button\n        style={{ ...style }}\n        size={size}\n        icon={<TableOutlined />}\n        title={t('change.columns')} // Use native title instead of Tooltip to avoid findDOMNode\n      >\n        {iconOnly ? null : t('Columns')}\n      </Button>\n    </Dropdown>\n  );\n};\n\nexport default FilterColumns;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,UAAU,QACL,MAAM;AACb,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAM;EAAEC;AAAK,CAAC,GAAGL,UAAU;AAE3B,MAAMM,aAAa,GAAGA,CAAC;EACrBC,OAAO,GAAG,EAAE;EACZC,UAAU;EACVC,KAAK;EACLC,IAAI,GAAG,EAAE;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEvC,MAAMsB,mBAAmB,GAAIC,IAAI,IAAK;IACpCF,OAAO,CAACE,IAAI,CAAC;EACf,CAAC;EAED,SAASC,QAAQA,CAACC,OAAO,EAAE;IACzB,MAAMC,QAAQ,GAAGb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,GAAG,CAAEC,IAAI,IAAK;MACtC,IAAIA,IAAI,CAACC,SAAS,KAAKJ,OAAO,CAACI,SAAS,EAAE;QACxCD,IAAI,CAACE,OAAO,GAAG,EAACF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEE,OAAO;MAC/B;MACA,OAAOF,IAAI;IACb,CAAC,CAAC;IACFd,UAAU,CAACY,QAAQ,CAAC;EACtB;;EAEA;EACA,MAAMK,SAAS,GAAG9B,OAAO,CAAC,MAAM;IAC9B,OAAO,CAAAY,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,GAAG,CAAC,CAACC,IAAI,EAAEI,GAAG,MAAM;MAClCA,GAAG,EAAG,GAAEJ,IAAI,CAACC,SAAS,IAAID,IAAI,CAACI,GAAG,IAAIA,GAAI,EAAC;MAC3CC,KAAK,eACHvB,OAAA,CAACN,KAAK;QAAC8B,SAAS,EAAC,gCAAgC;QAACnB,KAAK,EAAE;UAAEoB,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACzE1B,OAAA,CAACC,IAAI;UAAAyB,QAAA,EAAER,IAAI,CAACS;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/B,OAAA,CAACL,MAAM;UACLoB,OAAO,EAAEG,IAAI,CAACE,OAAQ;UACtBN,QAAQ,EAAEA,CAAA,KAAMA,QAAQ,CAACI,IAAI,CAAE;UAC/Bc,QAAQ,EAAEV,GAAG,KAAK,CAAE,CAAC;UAAA;UACrBhB,IAAI,EAAC;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR;MACD;MACAE,OAAO,EAAGC,CAAC,IAAK;QACdA,CAAC,CAACC,QAAQ,CAACC,eAAe,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,CAAC,KAAI,EAAE;EACX,CAAC,EAAE,CAACjC,OAAO,CAAC,CAAC;EAEb,oBACEH,OAAA,CAACP,QAAQ;IACP4C,IAAI,EAAE;MAAEC,KAAK,EAAEjB;IAAU,CAAE;IAC3BkB,OAAO,EAAE,CAAC,OAAO,CAAE;IACnBC,YAAY,EAAE5B,mBAAoB;IAClCF,IAAI,EAAEA,IAAK;IACX+B,SAAS,EAAC,aAAa;IAAAf,QAAA,eAEvB1B,OAAA,CAACR,MAAM;MACLa,KAAK,EAAE;QAAE,GAAGA;MAAM,CAAE;MACpBC,IAAI,EAAEA,IAAK;MACXoC,IAAI,eAAE1C,OAAA,CAACH,aAAa;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACxBJ,KAAK,EAAElB,CAAC,CAAC,gBAAgB,CAAE,CAAC;MAAA;MAAAiB,QAAA,EAE3BnB,QAAQ,GAAG,IAAI,GAAGE,CAAC,CAAC,SAAS;IAAC;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEf,CAAC;AAACvB,EAAA,CAhEIN,aAAa;EAAA,QAOHJ,cAAc;AAAA;AAAA6C,EAAA,GAPxBzC,aAAa;AAkEnB,eAAeA,aAAa;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}